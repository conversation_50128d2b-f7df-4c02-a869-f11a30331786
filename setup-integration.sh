#!/bin/bash

# Integration Test Setup Script
# This script helps set up the integration environment for frontend developers

set -e

echo "🚀 PathForge AI Integration Setup"
echo "=================================="

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker Desktop and try again."
    exit 1
fi

echo "✅ Docker is running"

# Check if docker-compose is available
if ! command -v docker-compose > /dev/null 2>&1; then
    echo "❌ docker-compose not found. Please install Docker Compose."
    exit 1
fi

echo "✅ Docker Compose is available"

# Validate the compose file
echo "🔍 Validating compose configuration..."
if docker-compose -f compose.integration.yml config > /dev/null 2>&1; then
    echo "✅ Compose file is valid"
else
    echo "❌ Compose file validation failed"
    exit 1
fi

# Check if .env file exists
if [[ -f .env ]]; then
    echo "✅ Environment file found"
else
    echo "⚠️  No .env file found, using defaults"
fi

# Try to pull or build images
echo "🔨 Attempting to build services..."

# Check if required source files exist
echo "🔍 Checking source files..."
if [[ ! -f "src/run_service.py" ]]; then
    echo "❌ Missing src/run_service.py"
    exit 1
fi

if [[ ! -d "src/memory" ]]; then
    echo "❌ Missing src/memory/ directory"
    exit 1
fi

if [[ ! -d "src/service" ]]; then
    echo "❌ Missing src/service/ directory"
    exit 1
fi

echo "✅ Required source files found"

# Check .dockerignore for potential exclusions
if grep -q "^src/memory/" .dockerignore 2>/dev/null; then
    echo "⚠️  Warning: .dockerignore may exclude required files"
fi

if docker-compose -f compose.integration.yml build; then
    echo "✅ Build successful"
    
    echo "🚀 Starting services..."
    if docker-compose -f compose.integration.yml up -d; then
        echo "✅ Services started successfully!"
        echo ""
        echo "🌐 Access points:"
        echo "   Agent Service: http://localhost:8000"
        echo "   Streamlit App: http://localhost:8501"
        echo ""
        echo "📋 Useful commands:"
        echo "   View logs: docker-compose -f compose.integration.yml logs -f"
        echo "   Stop services: docker-compose -f compose.integration.yml down"
        echo "   Restart: docker-compose -f compose.integration.yml restart"
    else
        echo "❌ Failed to start services"
        exit 1
    fi
else
    echo "❌ Build failed"
    echo ""
    echo "🔧 Troubleshooting steps:"
    echo "1. Check if required source files exist:"
    echo "   - src/run_service.py"
    echo "   - src/memory/ directory"
    echo "   - src/service/ directory"
    echo "2. Check .dockerignore file for file exclusions"
    echo "3. Verify Docker build context includes src/ directory"
    echo "4. Check Docker daemon status: docker info"
    echo "5. Try: docker system prune -f && docker volume prune -f"
    echo ""
    echo "📖 See INTEGRATION_SETUP.md for more details"
    exit 1
fi
