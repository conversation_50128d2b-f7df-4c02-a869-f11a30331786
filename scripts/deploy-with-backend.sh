#!/bin/bash

# Deploy Updated CodePlus Platform with PostgreSQL Backend
# This script deploys the platform with the new backend service and PostgreSQL configuration

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
STACK_NAME="pathforge-ai"
ENV_FILE="${ENV_FILE:-.env.production}"

echo -e "${BLUE}🚀 Deploying CodePlus Platform with PostgreSQL Backend${NC}"
echo "Stack Name: $STACK_NAME"
echo "Environment: $ENV_FILE"
echo ""

# Check if environment file exists
if [ ! -f "$ENV_FILE" ]; then
    echo -e "${RED}❌ Environment file $ENV_FILE not found!${NC}"
    echo -e "${YELLOW}Please create $ENV_FILE with the required configuration.${NC}"
    echo -e "${YELLOW}See .env.production or .env.staging for examples.${NC}"
    exit 1
fi

# Load environment variables
source "$ENV_FILE"

# Validate required environment variables
required_vars=(
    "POSTGRES_USER"
    "POSTGRES_PASSWORD" 
    "POSTGRES_DB"
    "POSTGRES_BACKEND_DB"
    "JWT_SECRET"
    "AUTH_SECRET"
)

echo -e "${BLUE}🔍 Validating environment variables...${NC}"
for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
        echo -e "${RED}❌ Required environment variable $var is not set${NC}"
        exit 1
    fi
    echo -e "${GREEN}✅ $var is set${NC}"
done

# Check Docker Swarm mode
if ! docker info --format '{{.Swarm.LocalNodeState}}' | grep -q "active"; then
    echo -e "${RED}❌ Docker Swarm is not active${NC}"
    echo -e "${YELLOW}Please initialize Docker Swarm with: docker swarm init${NC}"
    exit 1
fi

# Ensure Traefik network exists
if ! docker network ls | grep -q "traefik_main"; then
    echo -e "${YELLOW}⚠️  Creating traefik_main network...${NC}"
    docker network create --driver overlay traefik_main
fi

# Make database init script executable
chmod +x scripts/init-multiple-databases.sh

# Deploy the stack
echo -e "${BLUE}📦 Deploying stack...${NC}"
docker stack deploy \
    --compose-file docker-compose.swarm.yml \
    --with-registry-auth \
    "$STACK_NAME"

# Wait for services to be ready
echo -e "${BLUE}⏳ Waiting for services to start...${NC}"
sleep 10

# Check service status
echo -e "${BLUE}📊 Service Status:${NC}"
docker stack ps "$STACK_NAME" --format "table {{.Name}}\t{{.CurrentState}}\t{{.DesiredState}}\t{{.Error}}"

echo ""
echo -e "${GREEN}🎉 Deployment completed!${NC}"
echo ""
echo -e "${BLUE}📋 Service URLs:${NC}"
echo "  🎯 Agent Service: https://pathforge-ai-backend.csharpp.com"
echo "  🖥️  Frontend: https://pathforge-ai.csharpp.com"
echo "  🔧 API Backend: https://pathforge-ai-api.csharpp.com"
echo "  📊 Streamlit App: https://pathforge_ai-streamlit.csharpp.com"
echo ""
echo -e "${YELLOW}📝 Notes:${NC}"
echo "  • PostgreSQL creates separate databases for agent and backend services"
echo "  • Backend service runs Prisma migrations automatically on startup"
echo "  • Check logs with: docker service logs ${STACK_NAME}_<service_name>"
echo "  • Scale services with: docker service scale ${STACK_NAME}_<service_name>=<replicas>"
echo ""

# Show logs for critical services
echo -e "${BLUE}📋 Recent logs for PostgreSQL service:${NC}"
docker service logs --tail 20 "${STACK_NAME}_pathforge_ai_postgres" || echo "Service not ready yet"

echo ""
echo -e "${BLUE}📋 Recent logs for Backend service:${NC}"
docker service logs --tail 20 "${STACK_NAME}_pathforge_ai_backend" || echo "Service not ready yet"
