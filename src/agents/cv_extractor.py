import datetime
import json
import logging
import uuid
from typing import Dict, Any, Optional

from pypdf import PdfReader
from pypdf.errors import PdfReadError
from langchain.chat_models import init_chat_model
from langchain_core.messages import HumanMessage
from langchain_core.prompts import PromptTemplate
from langchain_openai import ChatOpenAI
from langgraph.checkpoint.memory import MemorySaver
from langgraph.prebuilt import create_react_agent

from agents.embedded_people_skills import ResumeDB, upsert_resume_chunks
from agents.prompt_lib import EXTRACTOR_AGENT_INSTRUCTIONS, JSON_OUTPUT_FORMAT
from core import get_model
from core.settings import settings
from schema.models import OpenAIModelName

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def extract_text_from_pdf(pdf_path):
    """
    Extracts pdf_text_content from a given PDF file.

    :param pdf_path: Path to the PDF file.
    :return: Cleaned extracted pdf_text_content as a string or None if an error occurs.
    """
    pdf_text_content = ""
    try:
        with open(pdf_path, 'rb') as file:
            pdf_reader = PdfReader(file)
            for page_num in range(len(pdf_reader.pages)):
                page = pdf_reader.pages[page_num]
                page_text = page.extract_text()
                if page_text:
                    pdf_text_content += page_text
    except FileNotFoundError:
        print(f"Error: PDF file not found at {pdf_path}")
    except PdfReadError as e:
        print(f"An error occurred while reading the PDF file: {e}")
    # Clean the pdf_text_content: trim and remove blank lines
    lines = [line.strip() for line in pdf_text_content.splitlines() if line.strip()]
    return "\n".join(lines)


def extract_json_from_response(response_content: str) -> Optional[Dict[str, Any]]:
    """
    Extract JSON content from the agent's response, handling various formats.

    Args:
        response_content: The raw response content from the agent

    Returns:
        Parsed JSON as a dictionary, or None if parsing fails
    """
    try:
        # Try to parse the entire response as JSON first
        return json.loads(response_content)
    except json.JSONDecodeError:
        # Look for JSON content within code blocks
        import re
        json_pattern = r'```(?:json)?\s*(\{.*?\})\s*```'
        matches = re.findall(json_pattern, response_content, re.DOTALL)

        for match in matches:
            try:
                return json.loads(match)
            except json.JSONDecodeError:
                continue

        # Look for JSON content without code blocks
        json_pattern = r'(\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\})'
        matches = re.findall(json_pattern, response_content, re.DOTALL)

        for match in matches:
            try:
                return json.loads(match)
            except json.JSONDecodeError:
                continue

        logger.warning(f"Could not extract valid JSON from response: {response_content[:200]}...")
        return None


def get_default_json_response() -> Dict[str, Any]:
    """
    Return the default JSON response structure when extraction fails.

    Returns:
        Default JSON structure based on JSON_OUTPUT_FORMAT
    """
    return json.loads(JSON_OUTPUT_FORMAT)


def init_agent(user_input: str) -> Dict[str, Any]:
    """
    Initialize and run the CV extraction agent with the provided input.

    Args:
        user_input: The CV/resume text content to be processed

    Returns:
        Extracted CV data in JSON format following the JSON_OUTPUT_FORMAT schema
    """
    try:
        memory = MemorySaver()
        # Use a more reliable model initialization
        try:
            model = get_model(OpenAIModelName.GPT_4O_MINI)
        except Exception as e:
            logger.warning(f"Failed to initialize gpt-4o-mini, falling back to ChatOpenAI: {e}")

        tools = []
        prompt_template = PromptTemplate.from_template(template=EXTRACTOR_AGENT_INSTRUCTIONS)
        formatted_prompt = prompt_template.format(
            date=str(datetime.datetime.now().date()),
            input=user_input,
            output_format=JSON_OUTPUT_FORMAT
        )

        extractor_agent = create_react_agent(model, tools, checkpointer=memory)
        config = {"configurable": {"thread_id": "abc123"}}

        logger.info("Running CV extraction agent...")
        final_message = None
        for step in extractor_agent.stream(
                {"messages": [HumanMessage(content=formatted_prompt)]},
                config,
                stream_mode="values",
        ):
            final_message = step["messages"][-1]

        if final_message and hasattr(final_message, 'content'):
            response_content = final_message.content
            logger.info("Agent response received, parsing JSON...")

            # Extract JSON from the response
            extracted_json = extract_json_from_response(response_content)

            if extracted_json:
                logger.info("Successfully extracted JSON from agent response")
                return extracted_json
            else:
                logger.error("Failed to extract valid JSON from agent response")
                return get_default_json_response()
        else:
            logger.error("No valid response received from agent")
            return get_default_json_response()

    except Exception as e:
        logger.error(f"Error in CV extraction agent: {str(e)}")
        return get_default_json_response()


def store_cv_in_database(
        cv_data: Dict[str, Any],
        user_id: Optional[str] = None,
        source_id: Optional[str] = None
) -> bool:
    """
    Store the extracted CV data in the PostgreSQL database using embedded_people_skills.

    Args:
        cv_data: The extracted CV data in JSON format
        user_id: Unique identifier for the user (auto-generated if not provided)
        source_id: Identifier for the source of the resume (auto-generated if not provided)

    Returns:
        True if database storage was successful, False otherwise
    """
    try:
        # Generate IDs if not provided
        if not user_id:
            # Use email as base for user_id if available, otherwise generate UUID
            email = cv_data.get("email", "")
            if email and email != "Not provided":
                user_id = email.split("@")[0] + "_" + str(uuid.uuid4())[:8]
            else:
                user_id = "user_" + str(uuid.uuid4())[:8]

        if not source_id:
            source_id = "cv_" + str(uuid.uuid4())[:8]

        # Get database connection URL
        db_url = settings.get_postgresql_url()
        logger.info(f"Attempting to store CV data for user_id: {user_id}, source_id: {source_id}")

        # Store the CV data using embedded_people_skills
        with ResumeDB(db_url) as db:
            upsert_resume_chunks(cv_data, user_id, source_id, db)

        logger.info(f"Successfully stored CV data in database for user: {user_id}")
        return True

    except Exception as e:
        logger.error(f"Failed to store CV data in database: {str(e)}")
        return False


def process_cv_extraction(
        query: str,
        store_in_db: bool = True,
        user_id: Optional[str] = None,
        source_id: Optional[str] = None
) -> Dict[str, Any]:
    """
    Main function to process CV/resume extraction tasks using the LangChain agent.

    This function serves as the primary interface for CV extraction, accepting a query
    (typically CV/resume text content) and returning structured JSON data following
    the predefined schema. Optionally stores the extracted data in the PostgreSQL database.

    Args:
        query: A string containing the CV/resume text content or extraction query
        store_in_db: Whether to store the extracted CV data in the database (default: True)
        user_id: Unique identifier for the user (auto-generated if not provided)
        source_id: Identifier for the source of the resume (auto-generated if not provided)

    Returns:
        A dictionary containing the extracted CV data in JSON format, structured
        according to the JSON_OUTPUT_FORMAT schema with fields like:
        - full_name
        - email
        - phone_number
        - total_years_experience
        - skills (categorized by experience level)
        - work_experience (array of job entries)
        - projects (array of project entries)
        - education (array of education entries)

    Note:
        The function will always return the extracted JSON data, even if database
        storage fails. Database errors are logged but do not affect the extraction result.
    """
    logger.info(f"Processing CV extraction for query of length: {len(query)} characters")

    if not query or not query.strip():
        logger.warning("Empty query provided, returning default response")
        return get_default_json_response()

    try:
        # Extract CV data using the LangChain agent
        result = init_agent(query.strip())
        logger.info("CV extraction completed successfully")

        # Store in database if requested and extraction was successful
        if store_in_db and result != get_default_json_response():
            logger.info("Attempting to store CV data in database...")
            db_success = store_cv_in_database(result, user_id, source_id)
            if db_success:
                logger.info("CV data successfully stored in database")
            else:
                logger.warning("CV data extraction succeeded but database storage failed")
        elif store_in_db:
            logger.warning("Skipping database storage due to extraction failure")
        else:
            logger.info("Database storage disabled, returning extraction result only")

        return result

    except Exception as e:
        logger.error(f"Error in process_cv_extraction: {str(e)}")
        return get_default_json_response()


def process_cv_with_auto_storage(query: str) -> Dict[str, Any]:
    """
    Convenience function to process CV extraction with automatic database storage.

    This function automatically generates user_id and source_id based on the extracted
    CV data and stores the result in the database.

    Args:
        query: A string containing the CV/resume text content or extraction query

    Returns:
        A dictionary containing the extracted CV data in JSON format
    """
    return process_cv_extraction(query, store_in_db=True)


def process_cv_extraction_only(query: str) -> Dict[str, Any]:
    """
    Convenience function to process CV extraction without database storage.

    This function only performs CV extraction and returns the JSON result
    without storing it in the database.

    Args:
        query: A string containing the CV/resume text content or extraction query

    Returns:
        A dictionary containing the extracted CV data in JSON format
    """
    return process_cv_extraction(query, store_in_db=False)


# if __name__ == '__main__':
#     pdf_path = "/Users/<USER>/Downloads/FPT_CV_LOILq7.pdf"
#     try:
#         cv_text = extract_text_from_pdf(pdf_path)
#         if cv_text:
#             logger.info(f"Extracted text from PDF: {len(cv_text)} characters")
#
#             # Extract and store in database (default behavior)
#             result = process_cv_extraction(
#                 cv_text,
#                 store_in_db=True,
#                 user_id="loilq7",  # Optional: specify user ID
#                 source_id="cv_loilq_pdf"  # Optional: specify source ID
#             )
#             print(f"CV of loilq7 Extraction Result (with database storage):")
#             print(json.dumps(result, indent=2, ensure_ascii=False))
#         else:
#             logger.error("Failed to extract text from PDF")
#     except Exception as e:
#         logger.error(f"Error processing PDF: {str(e)}")
