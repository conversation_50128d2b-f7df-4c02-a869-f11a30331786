FROM node:20-alpine

WORKDIR /app

# Install dos2unix to fix line endings
RUN apk add --no-cache dos2unix

# Upgrade npm to latest version to avoid compatibility issues
RUN npm install -g npm@latest

# Copy package files
COPY package.json package-lock.json ./

# Install dependencies
RUN npm ci

# Copy entrypoint script separately and fix it
COPY entrypoint.sh ./
RUN dos2unix ./entrypoint.sh && chmod +x ./entrypoint.sh

# Copy rest of source code
COPY . .

# Generate Prisma client
RUN npx prisma generate

# Expose the port
EXPOSE 8080

# Set the entrypoint
ENTRYPOINT ["/app/entrypoint.sh"]