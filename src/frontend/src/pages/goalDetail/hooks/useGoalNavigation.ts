import { useCallback } from 'react';
import { useNavigate } from 'react-router-dom';

/**
 * Custom hook for handling navigation actions in the goal detail page.
 * Provides consistent navigation handlers for different routes.
 * 
 * @returns Object containing navigation handler functions
 */
export function useGoalNavigation() {
  const navigate = useNavigate();

  const handleBackToGoals = useCallback(() => {
    navigate('/goals');
  }, [navigate]);

  const handleBackToHome = useCallback(() => {
    navigate('/');
  }, [navigate]);

  const navigateToGoal = useCallback((goalId: string) => {
    navigate(`/goals/${goalId}`);
  }, [navigate]);

  const navigateToGoalEdit = useCallback((goalId: string) => {
    navigate(`/goals/${goalId}/edit`);
  }, [navigate]);

  return {
    handleBackToGoals,
    handleBackToHome,
    navigateToGoal,
    navigateToGoalEdit,
  };
}
