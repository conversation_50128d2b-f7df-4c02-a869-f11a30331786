import { useCallback } from 'react';
import {
  addEdge,
  Background,
  Connection,
  Controls,
  MiniMap,
  Panel,
  ReactFlow,
  useEdgesState,
  useNodesState,
} from '@xyflow/react';
import { Text, Title } from '@mantine/core';
import { LearningEdge } from './edges/LearningEdge';
import { LearningNode } from './nodes/LearningNode';
import { useRoadmapData } from './hooks/useRoadmapData';
import {
  LearningNode as LearningNodeType,
  RoadmapFlowProps,
} from './types';
import classes from './RoadmapFlow.module.css';
// Import React Flow styles
import '@xyflow/react/dist/style.css';

const nodeTypes = {
  learningNode: LearningNode as any,
};

const edgeTypes = {
  learningEdge: LearningEdge as any,
};

export function RoadmapFlow({ goalId, data, onNodeClick, onProgressUpdate: _ }: RoadmapFlowProps) {
  const sampleData = useRoadmapData();
  const roadmapData = data || sampleData;

  const [nodes, , onNodesChange] = useNodesState(roadmapData.nodes as LearningNodeType[]);
  const [edges, setEdges, onEdgesChange] = useEdgesState(roadmapData.edges as any);

  const onConnect = useCallback(
    (params: Connection) => setEdges((eds) => addEdge(params, eds)),
    [setEdges]
  );

  const handleNodeClick = useCallback(
    (_: React.MouseEvent, node: any) => {
      if (onNodeClick) {
        onNodeClick(node as LearningNodeType);
      }
    },
    [onNodeClick]
  );

  return (
    <div className={classes.roadmapContainer}>
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={onConnect}
        onNodeClick={handleNodeClick}
        nodeTypes={nodeTypes}
        edgeTypes={edgeTypes}
        fitView
        fitViewOptions={{
          padding: 0.1,
          includeHiddenNodes: false,
          minZoom: 0.5,
          maxZoom: 1.5,
        }}
        className={classes.reactFlow}
      >
        <Background color="#f1f3f4" />
        <Controls className={classes.controls} />
        <MiniMap
          className={classes.minimap}
          nodeColor={(node) => {
            const data = node.data as any;
            if (data?.isCompleted) {
              return '#51cf66';
            }
            if (data?.progress > 0) {
              return '#ffd43b';
            }
            return '#868e96';
          }}
        />

        <Panel position="top-left" className={classes.panel}>
          <Title order={3} size="h4" mb="xs">
            Learning Roadmap
          </Title>
          <Text size="sm" c="dimmed">
            {goalId ? `Goal: ${goalId}` : 'Interactive learning path visualization'}
          </Text>
        </Panel>
      </ReactFlow>
    </div>
  );
}
