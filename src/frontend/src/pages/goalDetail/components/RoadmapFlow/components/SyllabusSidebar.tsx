import { IconBook, Icon<PERSON><PERSON>ck, IconClock, IconLock, IconTarget, IconX } from '@tabler/icons-react';
import {
  Badge,
  Button,
  Card,
  Divider,
  Group,
  List,
  Progress,
  Stack,
  Text,
  Title,
} from '@mantine/core';
import { LearningNodeData } from '../types';
import { useSyllabusData, useSyllabusProgress } from '../../../hooks';
import classes from './SyllabusSidebar.module.css';

interface SyllabusSidebarProps {
  nodeData: LearningNodeData | null;
  isOpen: boolean;
  onClose: () => void;
}

export function SyllabusSidebar({ nodeData, isOpen: _, onClose }: SyllabusSidebarProps) {
  if (!nodeData) {
    return null;
  }

  const syllabusData = useSyllabusData(nodeData.id);
  const { totalModules, moduleProgress } = useSyllabusProgress(syllabusData);

  return (
    <div className={classes.sidebar}>
      <div className={classes.sidebarHeader}>
        <Title order={3} size="h4">
          Course Syllabus
        </Title>
        <button type="button" className={classes.closeButton} onClick={onClose}>
          <IconX size={20} />
        </button>
      </div>

      <div>
        {/* Course Overview */}
        <Card my="md">
          <Stack gap="sm">
            <Group justify="space-between" align="flex-start">
              <div>
                <Title order={4} size="h5" mb="xs">
                  {nodeData.title}
                </Title>
                <Badge
                  size="sm"
                  color={
                    nodeData.level === 'beginner'
                      ? 'green'
                      : nodeData.level === 'intermediate'
                        ? 'yellow'
                        : 'red'
                  }
                  variant="light"
                >
                  {nodeData.level}
                </Badge>
              </div>
              {nodeData.isCompleted ? (
                <IconCheck size={24} color="var(--mantine-color-green-6)" />
              ) : nodeData.progress === 0 ? (
                <IconLock size={24} color="var(--mantine-color-gray-6)" />
              ) : (
                <IconBook size={24} color="var(--mantine-color-blue-6)" />
              )}
            </Group>

            <Text size="sm" c="dimmed">
              {nodeData.description}
            </Text>

            <Group gap="lg">
              <Group gap="xs">
                <IconClock size={16} />
                <Text size="sm">{nodeData.estimatedHours}h total</Text>
              </Group>
              <Group gap="xs">
                <IconTarget size={16} />
                <Text size="sm">{totalModules} modules</Text>
              </Group>
            </Group>

            <div>
              <Group justify="space-between" mb="xs">
                <Text size="sm" fw={500}>
                  Overall Progress
                </Text>
                <Text size="sm">{moduleProgress}%</Text>
              </Group>
              <Progress value={moduleProgress} color="blue" size="sm" radius="xl" />
            </div>
          </Stack>
        </Card>

        {/* Learning Objectives */}
        <Card my="md">
          <Title order={5} size="h6" mb="sm">
            Learning Objectives
          </Title>
          <List spacing="xs" size="sm">
            {syllabusData.objectives.map((objective, index) => (
              <List.Item key={index}>{objective}</List.Item>
            ))}
          </List>
        </Card>

        <Divider />

        {/* Course Modules */}
        <Card>
          <Title order={5} size="h6" mb="sm">
            Course Modules
          </Title>
          <Stack gap="xs">
            {syllabusData.modules.map((module, index) => (
              <Card key={index} withBorder padding="sm" radius="sm">
                <Group justify="space-between" align="center">
                  <div style={{ flex: 1 }}>
                    <Text size="sm" fw={500}>
                      {index + 1}. {module.title}
                    </Text>
                    <Text size="xs" c="dimmed">
                      {module.duration}
                    </Text>
                  </div>
                  {module.completed ? (
                    <IconCheck size={16} color="var(--mantine-color-green-6)" />
                  ) : (
                    <IconLock size={16} color="var(--mantine-color-gray-5)" />
                  )}
                </Group>
              </Card>
            ))}
          </Stack>
        </Card>

        <Divider />

        {/* Resources */}
        <Card>
          <Title order={5} size="h6" mb="sm">
            Resources
          </Title>
          <List spacing="xs" size="sm">
            {syllabusData.resources.map((resource, index) => (
              <List.Item key={index}>{resource}</List.Item>
            ))}
          </List>
        </Card>

        {/* Action Buttons */}
        <Card>
          <Stack gap="sm" mt="lg">
            <Button fullWidth variant="filled" disabled={nodeData.progress === 0}>
              {nodeData.isCompleted
                ? 'Review Course'
                : nodeData.progress > 0
                  ? 'Continue Learning'
                  : 'Start Course'}
            </Button>
            <Button fullWidth variant="light">
              View Full Curriculum
            </Button>
          </Stack>
        </Card>
      </div>
    </div>
  );
}
